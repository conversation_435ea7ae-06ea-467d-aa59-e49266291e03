local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Add required services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer.PlayerGui
local GameEvents = ReplicatedStorage.GameEvents

-- Cache frequently accessed objects
local workspace = workspace
local camera = workspace.CurrentCamera

-- Anti-AFK System
local VirtualInputManager = Instance.new("VirtualInputManager")
local Hooks = {}

local function TrueString(String)
    if type(String) ~= "string" then
        return false
    end

    return (string.split(String, "\0"))[1]
end

local function SortArguments(self, ...)
    return self, {...}
end

local function hookGetSerivce(...)
    local OldGetService; OldGetService = function(...)
        local self, Index = ...
        local Response = OldGetService(...)

        if type(Index) == "string" and TrueString(Index) == "VirtualInputManager" then
            error(("'%s' is not a valid Service name"):format(TrueString(Index)))
            return;
        end

        return Response
    end
end

local OldFindService = hookfunction(game.FindService, function(...)
    local self, Index = ...
    local Response = OldFindService(...)

    if type(Index) == "string" and TrueString(Index) == "VirtualInputManager" then
        return;
    end

    return Response
end)

hookGetSerivce(game.GetService)
hookGetSerivce(game.getService)
hookGetSerivce(game.service)

local OldNamecall; OldNamecall = hookmetamethod(game, "__namecall", function(...)
    local self, Arguments = SortArguments(...)
    local Method = getnamecallmethod()

    if typeof(self) == "Instance" and self == game and Method:lower():match("service") and TrueString(Arguments[1]) == "VirtualInputManager" then
        if Method == "FindService" then
            return;
        end

        local Success, Error = pcall(function()
            setnamecallmethod(Method)
            game[Method](game, "VirtualFuckOff")
        end)

        if not Error:match("is not a valid member") then
            error(Error:replace("VirtualFuckOff", "VirtualInputManager"))
            return;
        end
    end

    return OldNamecall(...)
end)

local OldWindow; OldWindow = hookmetamethod(UserInputService.WindowFocused, "__index", function(...)
    local self, Index = ...
    local Response = OldWindow(...)

    if type(Response) ~= "function" and (tostring(self):find("WindowFocused") or tostring(self):find("WindowFocusReleased")) and not table.find(Hooks, Response) then
        table.insert(Hooks, Response)

        if Index:lower() == "wait" then
            local Old2; Old2 = hookfunction(Response, function(...)
                local self1 = ...

                if self1 == self then
                    self1 = Instance.new("BindableEvent").Event
                end

                return Old2(self1)
            end)
        elseif Index:lower() == "connect" then
            local Old2; Old2 = hookfunction(Response, function(...)
                local self1, Function = ...

                if self1 == self then
                    Function = function() return; end
                end

                return Old2(self1, Function)
            end)
        end
    end

    return Response
end)

for i, v in next, getconnections(UserInputService.WindowFocusReleased) do
    v:Disable()
end

for i, v in next, getconnections(UserInputService.WindowFocused) do
    v:Disable()
end

if not iswindowactive() and not getgenv().WindowFocused then
    firesignal(UserInputService.WindowFocused)
    getgenv().WindowFocused = true
end

-- Anti-AFK variables
local antiAfkEnabled = false
local antiAfkConnection = nil

-- Anti-AFK function
local function startAntiAfk()
    if antiAfkConnection then return end

    antiAfkConnection = spawn(function()
        while antiAfkEnabled do
            VirtualInputManager:SendKeyEvent(true, Enum.KeyCode.Unknown, false, game)
            task.wait(Random.new():NextNumber(15, 120))
        end
    end)
end

local function stopAntiAfk()
    antiAfkEnabled = false
    if antiAfkConnection then
        antiAfkConnection = nil
    end
end

-- Workspace Settings System
local SettingsFolder = nil
local function initializeSettingsFolder()
    -- Create or find settings folder in workspace
    SettingsFolder = workspace:FindFirstChild("ScriptSettings")
    if not SettingsFolder then
        SettingsFolder = Instance.new("Folder")
        SettingsFolder.Name = "ScriptSettings"
        SettingsFolder.Parent = workspace
    end

    -- Create player-specific folder
    local playerFolder = SettingsFolder:FindFirstChild(LocalPlayer.Name)
    if not playerFolder then
        playerFolder = Instance.new("Folder")
        playerFolder.Name = LocalPlayer.Name
        playerFolder.Parent = SettingsFolder
    end

    return playerFolder
end

-- Settings save/load functions
local function saveSettingToWorkspace(settingName, value)
    local playerFolder = initializeSettingsFolder()

    -- Remove existing setting
    local existingSetting = playerFolder:FindFirstChild(settingName)
    if existingSetting then
        existingSetting:Destroy()
    end

    -- Create new setting
    local settingValue = Instance.new("StringValue")
    settingValue.Name = settingName
    settingValue.Value = game:GetService("HttpService"):JSONEncode(value)
    settingValue.Parent = playerFolder
end

local function loadSettingFromWorkspace(settingName, defaultValue)
    local playerFolder = initializeSettingsFolder()
    local settingValue = playerFolder:FindFirstChild(settingName)

    if settingValue and settingValue:IsA("StringValue") then
        local success, result = pcall(function()
            return game:GetService("HttpService"):JSONDecode(settingValue.Value)
        end)

        if success then
            return result
        end
    end

    return defaultValue
end

-- Seed stock variables
local SeedStock = {}
local SelectedSeedStock = { Selected = {} }
local lastStockCheck = {}
local autoBuyEnabled = false
local stockCheckConnection = nil

-- Gear stock variables
local GearStock = {}
local SelectedGearStock = { Selected = {} }
local lastGearStockCheck = {}
local gearAutoBuyEnabled = false
local gearStockCheckConnection = nil

-- Egg stock variables
local EggStock = {}
local SelectedEggStock = { Selected = {} }
local lastEggStockCheck = {}
local eggAutoBuyEnabled = false
local eggStockCheckConnection = nil

-- Farm system variables
local OwnedSeeds = {}
local SelectedFarmSeed = { Selected = "" }
local AutoPlant = false
local AutoHarvest = false
local AutoSell = false
local AutoWalk = false
local NoClip = false
local AutoPlantRandom = false
local AutoWalkAllowRandom = true
local SellThreshold = 15
local AutoWalkMaxWait = 10
local IsSelling = false
local HarvestIgnores = {
    Normal = false,
    Gold = false,
    Rainbow = false
}

-- Farm connections
local autoPlantConnection = nil
local autoHarvestConnection = nil
local autoWalkConnection = nil
local noClipConnection = nil

-- Function to get seed stock (simplified)
local function GetSeedStock(IgnoreNoStock)
	local SeedShop = PlayerGui:FindFirstChild("Seed_Shop")
	if not SeedShop then return IgnoreNoStock and {} or SeedStock end

	local Items = SeedShop:FindFirstChild("Blueberry", true)
	if not Items then return IgnoreNoStock and {} or SeedStock end
	Items = Items.Parent

	local NewList = {}

	for _, Item in pairs(Items:GetChildren()) do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if MainFrame then
			local StockText = MainFrame:FindFirstChild("Stock_Text")
			if StockText then
				local StockCount = tonumber(StockText.Text:match("%d+")) or 0

				if IgnoreNoStock then
					if StockCount > 0 then
						NewList[Item.Name] = StockCount
					end
				else
					SeedStock[Item.Name] = StockCount
				end
			end
		end
	end

	return IgnoreNoStock and NewList or SeedStock
end

-- Function to buy seed
local function BuySeed(Seed)
	local buyEvent = GameEvents:FindFirstChild("BuySeedStock")
	if buyEvent then
		buyEvent:FireServer(Seed)
	end
end

-- Function to get gear stock (simplified)
local function GetGearStock(IgnoreNoStock)
	local GearShop = PlayerGui:FindFirstChild("Gear_Shop")
	if not GearShop then return IgnoreNoStock and {} or GearStock end

	-- Try to find gear items - assuming similar structure to seed shop
	local Items = GearShop:FindFirstChild("WateringCan")
	if not Items then
		-- Try to find any frame that contains gear items
		for _, child in pairs(GearShop:GetDescendants()) do
			if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
				Items = child.Parent
				break
			end
		end
	end

	if not Items then
		return IgnoreNoStock and {} or GearStock
	end

	local NewList = {}

	for _, Item in pairs(Items:GetChildren()) do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if MainFrame then
			local StockText = MainFrame:FindFirstChild("Stock_Text")
			if StockText then
				local StockCount = tonumber(StockText.Text:match("%d+")) or 0

				if IgnoreNoStock then
					if StockCount > 0 then
						NewList[Item.Name] = StockCount
					end
				else
					GearStock[Item.Name] = StockCount
				end
			end
		end
	end

	return IgnoreNoStock and NewList or GearStock
end

-- Function to buy gear
local function BuyGear(Gear)
	local buyEvent = GameEvents:FindFirstChild("BuyGearStock")
	if buyEvent then
		buyEvent:FireServer(Gear)
	end
end

-- Function to get egg stock (simplified)
local function GetEggStock(IgnoreNoStock)
	-- Try multiple possible names for egg shop
	local EggShop = PlayerGui:FindFirstChild("PetShop_UI")
		or PlayerGui:FindFirstChild("Pet_Egg_Shop")
		or PlayerGui:FindFirstChild("Egg_Shop")
		or PlayerGui:FindFirstChild("PetEggShop")
		or PlayerGui:FindFirstChild("EggShop")

	if not EggShop then
		return IgnoreNoStock and {} or EggStock
	end

	-- Try to find egg items container - look for common patterns
	local Items = nil

	-- Method 1: Look for frames with Main_Frame children
	for _, child in pairs(EggShop:GetDescendants()) do
		if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
			Items = child.Parent
			break
		end
	end

	-- Method 2: Look for ScrollingFrame or Frame that contains multiple items
	if not Items then
		for _, child in pairs(EggShop:GetDescendants()) do
			if child:IsA("ScrollingFrame") or child:IsA("Frame") then
				local childCount = 0
				for _, subchild in pairs(child:GetChildren()) do
					if subchild:IsA("Frame") then
						childCount = childCount + 1
					end
				end
				if childCount > 1 then
					Items = child
					break
				end
			end
		end
	end

	if not Items then
		return IgnoreNoStock and {} or EggStock
	end

	local NewList = {}

	for _, Item in pairs(Items:GetChildren()) do
		if Item:IsA("Frame") then
			local MainFrame = Item:FindFirstChild("Main_Frame")
			if MainFrame then
				local StockText = MainFrame:FindFirstChild("Stock_Text")
				if StockText then
					local StockCount = tonumber(StockText.Text:match("%d+")) or 0

					if IgnoreNoStock then
						if StockCount > 0 then
							NewList[Item.Name] = StockCount
						end
					else
						EggStock[Item.Name] = StockCount
					end
				end
			end
		end
	end

	return IgnoreNoStock and NewList or EggStock
end

-- Function to buy egg (simplified)
local function BuyEgg(Egg)
	-- Try different possible names for egg buying
	local possibleNames = {
		"BuyEggStock",
		"BuyEgg",
		"PurchaseEgg",
		"BuyPetEgg",
		"PetEggPurchase",
		"EggPurchase",
		"BuyPet",
		"PurchasePet"
	}

	for _, eventName in pairs(possibleNames) do
		local event = GameEvents:FindFirstChild(eventName)
		if event and event:IsA("RemoteEvent") then
			event:FireServer(Egg)
			break
		end
	end
end

-- Function to buy all selected eggs (simplified)
local function BuyAllSelectedEggs()
    local selectedEggs = SelectedEggStock.Selected
    if type(selectedEggs) ~= "table" then return end

    -- Check if we have any selected eggs - handle both table and other formats
    local eggsToProcess = {}

    for k, v in pairs(selectedEggs) do
        if v == true or (type(v) == "string" and v ~= "") then
            table.insert(eggsToProcess, type(k) == "string" and k or v)
        elseif type(k) == "number" and type(v) == "string" and v ~= "" then
            table.insert(eggsToProcess, v)
        end
    end

    if #eggsToProcess == 0 then return end

    -- Update stock before buying
    GetEggStock()

    -- Iterate through all selected eggs
    for _, eggName in pairs(eggsToProcess) do
        local stock = EggStock[eggName]

        -- Only buy if egg is in stock
        if stock and stock > 0 then
            for i = 1, stock do
                BuyEgg(eggName)
            end
        end
    end
end

-- Function to buy all selected gears (simplified)
local function BuyAllSelectedGears()
    local selectedGears = SelectedGearStock.Selected
    if type(selectedGears) ~= "table" then return end

    -- Check if we have any selected gears - handle both table and other formats
    local gearsToProcess = {}

    for k, v in pairs(selectedGears) do
        if v == true or (type(v) == "string" and v ~= "") then
            table.insert(gearsToProcess, type(k) == "string" and k or v)
        elseif type(k) == "number" and type(v) == "string" and v ~= "" then
            table.insert(gearsToProcess, v)
        end
    end

    if #gearsToProcess == 0 then return end

    -- Update stock before buying
    GetGearStock()

    -- Iterate through all selected gears
    for _, gearName in pairs(gearsToProcess) do
        local stock = GearStock[gearName]

        -- Only buy if gear is in stock
        if stock and stock > 0 then
            for i = 1, stock do
                BuyGear(gearName)
            end
        end
    end
end

-- Function to buy all selected seeds (simplified)
local function BuyAllSelectedSeeds()
    local selectedSeeds = SelectedSeedStock.Selected
    if type(selectedSeeds) ~= "table" then return end

    -- Check if we have any selected seeds - handle both table and other formats
    local seedsToProcess = {}

    for k, v in pairs(selectedSeeds) do
        if v == true or (type(v) == "string" and v ~= "") then
            table.insert(seedsToProcess, type(k) == "string" and k or v)
        elseif type(k) == "number" and type(v) == "string" and v ~= "" then
            table.insert(seedsToProcess, v)
        end
    end

    if #seedsToProcess == 0 then return end

    -- Update stock before buying
    GetSeedStock()

    -- Iterate through all selected seeds
    for _, seedName in pairs(seedsToProcess) do
        local stock = SeedStock[seedName]

        -- Only buy if seed is in stock
        if stock and stock > 0 then
            for i = 1, stock do
                BuySeed(seedName)
            end
        end
    end
end

-- Function to check stock changes and auto-buy (simplified)
local function startStockChecking()
    stockCheckConnection = RunService.Heartbeat:Connect(function()
        if not autoBuyEnabled or not SelectedSeedStock.Selected then return end

        -- Check if we have selected seeds
        local selectedSeeds = SelectedSeedStock.Selected
        if type(selectedSeeds) ~= "table" then return end

        local hasSelectedSeeds = false
        for k, v in pairs(selectedSeeds) do
            if v == true or (type(v) == "string" and v ~= "") then
                hasSelectedSeeds = true
                break
            end
        end

        if not hasSelectedSeeds then return end

        local currentStock = GetSeedStock()
        local shouldBuy = false

        -- Process selected seeds based on their format
        local seedsToCheck = {}
        for k, v in pairs(selectedSeeds) do
            if v == true or (type(v) == "string" and v ~= "") then
                local seedName = type(k) == "string" and k or v
                if type(seedName) == "string" and seedName ~= "" then
                    table.insert(seedsToCheck, seedName)
                end
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(seedsToCheck, v)
            end
        end

        -- Check if stock has changed for any selected seed OR if we haven't checked before
        for _, seedName in pairs(seedsToCheck) do
            local previousStock = lastStockCheck[seedName] or -1
            local newStock = currentStock[seedName] or 0

            if previousStock ~= newStock then
                lastStockCheck[seedName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedSeeds()
        end
    end)
end

-- Function to stop stock checking
local function stopStockChecking()
    if stockCheckConnection then
        stockCheckConnection:Disconnect()
        stockCheckConnection = nil
    end
end

-- Function to check gear stock changes and auto-buy (simplified)
local function startGearStockChecking()
    gearStockCheckConnection = RunService.Heartbeat:Connect(function()
        if not gearAutoBuyEnabled or not SelectedGearStock.Selected then return end

        -- Check if we have selected gears
        local selectedGears = SelectedGearStock.Selected
        if type(selectedGears) ~= "table" then return end

        local hasSelectedGears = false
        for k, v in pairs(selectedGears) do
            if v == true or (type(v) == "string" and v ~= "") then
                hasSelectedGears = true
                break
            end
        end

        if not hasSelectedGears then return end

        local currentStock = GetGearStock()
        local shouldBuy = false

        -- Process selected gears based on their format
        local gearsToCheck = {}
        for k, v in pairs(selectedGears) do
            if v == true or (type(v) == "string" and v ~= "") then
                local gearName = type(k) == "string" and k or v
                if type(gearName) == "string" and gearName ~= "" then
                    table.insert(gearsToCheck, gearName)
                end
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(gearsToCheck, v)
            end
        end

        -- Check if stock has changed for any selected gear OR if we haven't checked before
        for _, gearName in pairs(gearsToCheck) do
            local previousStock = lastGearStockCheck[gearName] or -1
            local newStock = currentStock[gearName] or 0

            if previousStock ~= newStock then
                lastGearStockCheck[gearName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedGears()
        end
    end)
end

-- Function to stop gear stock checking
local function stopGearStockChecking()
    if gearStockCheckConnection then
        gearStockCheckConnection:Disconnect()
        gearStockCheckConnection = nil
    end
end

-- Function to check egg stock changes and auto-buy (simplified)
local function startEggStockChecking()
    eggStockCheckConnection = RunService.Heartbeat:Connect(function()
        if not eggAutoBuyEnabled or not SelectedEggStock.Selected then return end

        -- Check if we have selected eggs
        local selectedEggs = SelectedEggStock.Selected
        if type(selectedEggs) ~= "table" then return end

        local hasSelectedEggs = false
        for k, v in pairs(selectedEggs) do
            if v == true or (type(v) == "string" and v ~= "") then
                hasSelectedEggs = true
                break
            end
        end

        if not hasSelectedEggs then return end

        local currentStock = GetEggStock()
        local shouldBuy = false

        -- Process selected eggs based on their format
        local eggsToCheck = {}
        for k, v in pairs(selectedEggs) do
            if v == true or (type(v) == "string" and v ~= "") then
                local eggName = type(k) == "string" and k or v
                if type(eggName) == "string" and eggName ~= "" then
                    table.insert(eggsToCheck, eggName)
                end
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(eggsToCheck, v)
            end
        end

        -- Check if stock has changed for any selected egg OR if we haven't checked before
        for _, eggName in pairs(eggsToCheck) do
            local previousStock = lastEggStockCheck[eggName] or -1
            local newStock = currentStock[eggName] or 0

            if previousStock ~= newStock then
                lastEggStockCheck[eggName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedEggs()
        end
    end)
end

-- Function to stop egg stock checking
local function stopEggStockChecking()
    if eggStockCheckConnection then
        eggStockCheckConnection:Disconnect()
        eggStockCheckConnection = nil
    end
end

-- Farm system functions
local function Plant(Position, Seed)
    -- Try multiple possible names for plant event
    local possibleNames = {
        "Plant_RE",
        "PlantSeed",
        "Plant",
        "PlantEvent",
        "SeedPlant"
    }

    for _, eventName in pairs(possibleNames) do
        local plantEvent = GameEvents:FindFirstChild(eventName)
        if plantEvent and plantEvent:IsA("RemoteEvent") then
            print("Planting", Seed, "at", Position) -- Debug message
            plantEvent:FireServer(Position, Seed)
            task.wait(0.3)
            return true
        end
    end

    print("Plant event not found!") -- Debug message
    return false
end

local function GetFarms()
    return workspace.Farm:GetChildren()
end

local function GetFarmOwner(Farm)
    local Important = Farm:FindFirstChild("Important")
    if not Important then return nil end
    local Data = Important:FindFirstChild("Data")
    if not Data then return nil end
    local Owner = Data:FindFirstChild("Owner")
    if not Owner then return nil end
    return Owner.Value
end

local function GetFarm(PlayerName)
    local Farms = GetFarms()
    for _, Farm in pairs(Farms) do
        local Owner = GetFarmOwner(Farm)
        if Owner == PlayerName then
            return Farm
        end
    end
    return nil
end

local function SellInventory()
    local Character = LocalPlayer.Character
    if not Character then return end

    local Previous = Character:GetPivot()
    local Leaderstats = LocalPlayer:FindFirstChild("leaderstats")
    if not Leaderstats then return end

    local ShecklesCount = Leaderstats:FindFirstChild("Sheckles")
    if not ShecklesCount then return end

    local PreviousSheckles = ShecklesCount.Value

    -- Prevent conflict
    if IsSelling then return end
    IsSelling = true

    Character:PivotTo(CFrame.new(62, 4, -26))

    local attempts = 0
    while attempts < 50 do -- Prevent infinite loop
        task.wait(0.1)
        attempts = attempts + 1

        if ShecklesCount.Value ~= PreviousSheckles then
            break
        end

        local sellEvent = GameEvents:FindFirstChild("Sell_Inventory")
        if sellEvent then
            sellEvent:FireServer()
        end
    end

    Character:PivotTo(Previous)
    task.wait(0.2)
    IsSelling = false
end

local function GetSeedInfo(Seed)
    if not Seed then return nil, nil end
    local PlantName = Seed:FindFirstChild("Plant_Name")
    local Count = Seed:FindFirstChild("Numbers")
    if not PlantName then return nil, nil end
    return PlantName.Value, Count and Count.Value or 0
end

local function CollectSeedsFromParent(Parent, Seeds)
    for _, Tool in pairs(Parent:GetChildren()) do
        local Name, Count = GetSeedInfo(Tool)
        if Name then
            Seeds[Name] = {
                Count = Count,
                Tool = Tool
            }
        end
    end
end

local function CollectCropsFromParent(Parent, Crops)
    for _, Tool in pairs(Parent:GetChildren()) do
        local Name = Tool:FindFirstChild("Item_String")
        if Name then
            table.insert(Crops, Tool)
        end
    end
end

local function GetOwnedSeeds()
    local Character = LocalPlayer.Character
    local Backpack = LocalPlayer.Backpack

    OwnedSeeds = {} -- Clear previous data

    if Backpack then
        CollectSeedsFromParent(Backpack, OwnedSeeds)
    end
    if Character then
        CollectSeedsFromParent(Character, OwnedSeeds)
    end

    return OwnedSeeds
end

local function GetInvCrops()
    local Character = LocalPlayer.Character
    local Backpack = LocalPlayer.Backpack
    local Crops = {}

    if Backpack then
        CollectCropsFromParent(Backpack, Crops)
    end
    if Character then
        CollectCropsFromParent(Character, Crops)
    end

    return Crops
end

local function GetArea(Base)
    if not Base then return 0, 0, 0, 0 end
    local Center = Base:GetPivot()
    local Size = Base.Size

    -- Bottom left
    local X1 = math.ceil(Center.X - (Size.X/2))
    local Z1 = math.ceil(Center.Z - (Size.Z/2))

    -- Top right
    local X2 = math.floor(Center.X + (Size.X/2))
    local Z2 = math.floor(Center.Z + (Size.Z/2))

    return X1, Z1, X2, Z2
end

local function EquipCheck(Tool)
    local Character = LocalPlayer.Character
    if not Character then return end
    local Humanoid = Character:FindFirstChild("Humanoid")
    local Backpack = LocalPlayer.Backpack

    if Tool and Tool.Parent == Backpack and Humanoid then
        Humanoid:EquipTool(Tool)
    end
end

-- Auto farm functions
local function GetRandomFarmPoint()
    local MyFarm = GetFarm(LocalPlayer.Name)
    if not MyFarm then return Vector3.new(0, 4, 0) end

    local Important = MyFarm:FindFirstChild("Important")
    if not Important then return Vector3.new(0, 4, 0) end

    local PlantLocations = Important:FindFirstChild("Plant_Locations")
    if not PlantLocations then return Vector3.new(0, 4, 0) end

    local FarmLands = PlantLocations:GetChildren()
    if #FarmLands == 0 then return Vector3.new(0, 4, 0) end

    local FarmLand = FarmLands[math.random(1, #FarmLands)]
    local X1, Z1, X2, Z2 = GetArea(FarmLand)
    local X = math.random(X1, X2)
    local Z = math.random(Z1, Z2)
    return Vector3.new(X, 4, Z)
end

local function AutoPlantLoop()
    if not AutoPlant then return end

    local Seed = SelectedFarmSeed.Selected
    if not Seed or Seed == "" then return end

    -- Try to plant without checking inventory first (as per user preference)
    local MyFarm = GetFarm(LocalPlayer.Name)
    if not MyFarm then return end

    local Important = MyFarm:FindFirstChild("Important")
    if not Important then return end

    local PlantLocations = Important:FindFirstChild("Plant_Locations")
    if not PlantLocations then return end

    local Dirt = PlantLocations:FindFirstChildOfClass("Part")
    if not Dirt then return end

    local X1, Z1, X2, Z2 = GetArea(Dirt)

    -- Plant at random points
    if AutoPlantRandom then
        for i = 1, 10 do -- Plant 10 seeds at random points
            local Point = GetRandomFarmPoint()
            Plant(Point, Seed)
        end
        return
    end

    -- Plant on the farmland area
    local Planted = 0
    local Step = 2 -- Increase step size for better spacing
    local MaxPlants = 50 -- Limit number of plants to prevent lag

    for X = X1, X2, Step do
        for Z = Z1, Z2, Step do
            if Planted >= MaxPlants then break end
            local Point = Vector3.new(X, 0.13, Z)
            Plant(Point, Seed)
            Planted = Planted + 1
        end
        if Planted >= MaxPlants then break end
    end
end

local function HarvestPlant(Plant)
    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
    -- Check if it can be harvested
    if not Prompt then return end
    fireproximityprompt(Prompt)
end

local function CanHarvest(Plant)
    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
    if not Prompt then return false end
    if not Prompt.Enabled then return false end
    return true
end

local function CollectHarvestable(Parent, Plants, IgnoreDistance)
    local Character = LocalPlayer.Character
    if not Character then return Plants end

    local PlayerPosition = Character:GetPivot().Position

    for _, Plant in pairs(Parent:GetChildren()) do
        -- Fruits
        local Fruits = Plant:FindFirstChild("Fruits")
        if Fruits then
            CollectHarvestable(Fruits, Plants, IgnoreDistance)
        end

        -- Distance check
        local PlantPosition = Plant:GetPivot().Position
        local Distance = (PlayerPosition - PlantPosition).Magnitude
        if not IgnoreDistance and Distance > 15 then
            continue
        end

        -- Ignore check
        local Variant = Plant:FindFirstChild("Variant")
        if Variant and HarvestIgnores[Variant.Value] then
            continue
        end

        -- Collect
        if CanHarvest(Plant) then
            table.insert(Plants, Plant)
        end
    end
    return Plants
end

local function GetHarvestablePlants(IgnoreDistance)
    local MyFarm = GetFarm(LocalPlayer.Name)
    if not MyFarm then return {} end

    local Important = MyFarm:FindFirstChild("Important")
    if not Important then return {} end

    local PlantsPhysical = Important:FindFirstChild("Plants_Physical")
    if not PlantsPhysical then return {} end

    local Plants = {}
    CollectHarvestable(PlantsPhysical, Plants, IgnoreDistance)
    return Plants
end

local function HarvestPlants()
    if not AutoHarvest then return end

    local Plants = GetHarvestablePlants()
    for _, Plant in pairs(Plants) do
        HarvestPlant(Plant)
    end
end

local function AutoSellCheck()
    if not AutoSell then return end

    local CropCount = #GetInvCrops()
    if CropCount < SellThreshold then return end

    SellInventory()
end

local function AutoWalkLoop()
    if not AutoWalk then return end
    if IsSelling then return end

    local Character = LocalPlayer.Character
    if not Character then return end

    local Humanoid = Character:FindFirstChild("Humanoid")
    if not Humanoid then return end

    local Plants = GetHarvestablePlants(true)
    local DoRandom = #Plants == 0 or math.random(1, 3) == 2

    -- Random point
    if AutoWalkAllowRandom and DoRandom then
        local Position = GetRandomFarmPoint()
        Humanoid:MoveTo(Position)
        return
    end

    -- Move to each plant
    for _, Plant in pairs(Plants) do
        local Position = Plant:GetPivot().Position
        Humanoid:MoveTo(Position)
    end
end

local function NoclipLoop()
    if not NoClip then return end

    local Character = LocalPlayer.Character
    if not Character then return end

    for _, Part in pairs(Character:GetDescendants()) do
        if Part:IsA("BasePart") then
            Part.CanCollide = false
        end
    end
end

-- Farm loop management
local function startFarmLoops()
    -- Auto-Walk
    if AutoWalk and not autoWalkConnection then
        autoWalkConnection = RunService.Heartbeat:Connect(function()
            AutoWalkLoop()
            task.wait(math.random(1, AutoWalkMaxWait))
        end)
    end

    -- Auto-Harvest
    if AutoHarvest and not autoHarvestConnection then
        autoHarvestConnection = RunService.Heartbeat:Connect(function()
            HarvestPlants()
            task.wait(0.1)
        end)
    end

    -- Auto-Plant
    if AutoPlant and not autoPlantConnection then
        autoPlantConnection = spawn(function()
            while AutoPlant do
                AutoPlantLoop()
                task.wait(5) -- Wait 5 seconds between planting cycles
            end
        end)
    end

    -- NoClip
    if NoClip and not noClipConnection then
        noClipConnection = RunService.Stepped:Connect(NoclipLoop)
    end
end

local function stopFarmLoops()
    if autoWalkConnection then
        autoWalkConnection:Disconnect()
        autoWalkConnection = nil
    end
    if autoHarvestConnection then
        autoHarvestConnection:Disconnect()
        autoHarvestConnection = nil
    end
    if autoPlantConnection then
        autoPlantConnection:Disconnect()
        autoPlantConnection = nil
    end
    if noClipConnection then
        noClipConnection:Disconnect()
        noClipConnection = nil
    end
end

local Window = Rayfield:CreateWindow({
    Name = "หมา " .. 1.0,
    LoadingTitle = "by XZery",
    LoadingSubtitle = "Loading...",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "RayfieldScriptHub",
        FileName = "grow-a-garden"
    },
    Discord = {
        Enabled = false,
        Invite = "noinvitelink",
        RememberJoins = true
    },
    KeySystem = false,
    KeySettings = {
        Title = "Untitled",
        Subtitle = "Key System",
        Note = "No method of obtaining the key is provided",
        FileName = "Key",
        SaveKey = true,
        GrabKeyFromSite = false,
        Key = {"Hello"}
    }
})

local Tabs = {
    Main = Window:CreateTab("Main", 4483362458),
    Seed = Window:CreateTab("Seed", 4483362458),
    Gear = Window:CreateTab("Gear", 4483362458),
    Egg = Window:CreateTab("Egg", 4483362458),
    Farm = Window:CreateTab("Farm", 4483362458)
}

local speed = Tabs.Main:CreateSlider({
    Name = "Speed",
    Range = {16, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 16,
    Flag = "SpeedSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.WalkSpeed = Value
        end
    end,
})

local jump = Tabs.Main:CreateSlider({
    Name = "Jump Power",
    Range = {50, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 50,
    Flag = "JumpSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.JumpPower = Value
        end
    end,
})

    -- Fly toggle functionality with mobile support (optimized)
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil
    local flySpeed = 50

    -- Mobile fly controls
    local flyUpButton = nil
    local flyDownButton = nil
    local mobileControlsFrame = nil
    local isTouchEnabled = UserInputService.TouchEnabled

    -- Cache color values
    local upButtonColor = Color3.fromRGB(0, 162, 255)
    local downButtonColor = Color3.fromRGB(255, 87, 87)
    local whiteColor = Color3.fromRGB(255, 255, 255)

    local function createMobileControls()
        if not isTouchEnabled then return end

        -- Create mobile fly controls
        local screenGui = Instance.new("ScreenGui")
        screenGui.Name = "FlyControls"
        screenGui.Parent = LocalPlayer.PlayerGui
        screenGui.ResetOnSpawn = false

        mobileControlsFrame = Instance.new("Frame")
        mobileControlsFrame.Name = "FlyControlsFrame"
        mobileControlsFrame.Size = UDim2.new(0, 120, 0, 200)
        mobileControlsFrame.Position = UDim2.new(1, -130, 0.5, -100)
        mobileControlsFrame.BackgroundTransparency = 1
        mobileControlsFrame.Parent = screenGui

        -- Fly Up Button
        flyUpButton = Instance.new("TextButton")
        flyUpButton.Name = "FlyUpButton"
        flyUpButton.Size = UDim2.new(0, 100, 0, 80)
        flyUpButton.Position = UDim2.new(0, 10, 0, 10)
        flyUpButton.BackgroundColor3 = upButtonColor
        flyUpButton.BorderSizePixel = 0
        flyUpButton.Text = "UP"
        flyUpButton.TextColor3 = whiteColor
        flyUpButton.TextScaled = true
        flyUpButton.Font = Enum.Font.GothamBold
        flyUpButton.Parent = mobileControlsFrame

        -- Add rounded corners to up button
        local upCorner = Instance.new("UICorner")
        upCorner.CornerRadius = UDim.new(0, 10)
        upCorner.Parent = flyUpButton

        -- Fly Down Button
        flyDownButton = Instance.new("TextButton")
        flyDownButton.Name = "FlyDownButton"
        flyDownButton.Size = UDim2.new(0, 100, 0, 80)
        flyDownButton.Position = UDim2.new(0, 10, 0, 110)
        flyDownButton.BackgroundColor3 = downButtonColor
        flyDownButton.BorderSizePixel = 0
        flyDownButton.Text = "DOWN"
        flyDownButton.TextColor3 = whiteColor
        flyDownButton.TextScaled = true
        flyDownButton.Font = Enum.Font.GothamBold
        flyDownButton.Parent = mobileControlsFrame

        -- Add rounded corners to down button
        local downCorner = Instance.new("UICorner")
        downCorner.CornerRadius = UDim.new(0, 10)
        downCorner.Parent = flyDownButton
    end

    local function removeMobileControls()
        if mobileControlsFrame and mobileControlsFrame.Parent then
            mobileControlsFrame.Parent:Destroy()
        end
        flyUpButton = nil
        flyDownButton = nil
        mobileControlsFrame = nil
    end

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Create mobile controls if on mobile
        createMobileControls()

        -- Movement control (optimized)
        connection = RunService.Heartbeat:Connect(function()
            local velocity = Vector3.new(0, 0, 0)

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- PC Controls (Keyboard)
            if not isTouchEnabled then
                if UserInputService:IsKeyDown(Enum.KeyCode.W) then
                    velocity = velocity + lookDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.S) then
                    velocity = velocity - lookDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.A) then
                    velocity = velocity - rightDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.D) then
                    velocity = velocity + rightDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.Space) then
                    velocity = velocity + upDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                    velocity = velocity - upDirection * flySpeed
                end
            else
                -- Mobile Controls
                local moveVector = char.Humanoid.MoveDirection
                if moveVector.Magnitude > 0 then
                    -- Use the movement direction from mobile control
                    local cameraCFrame = camera.CFrame
                    local relativeDirection = cameraCFrame:VectorToWorldSpace(Vector3.new(moveVector.X, 0, -moveVector.Z))
                    velocity = velocity + relativeDirection * flySpeed
                end

                -- Handle up/down movement with buttons
                if flyUpButton and flyUpButton.Parent then
                    -- Connect touch events for up button
                    if not flyUpButton:GetAttribute("Connected") then
                        flyUpButton:SetAttribute("Connected", true)
                        flyUpButton.MouseButton1Down:Connect(function()
                            flyUpButton:SetAttribute("Pressed", true)
                        end)
                        flyUpButton.MouseButton1Up:Connect(function()
                            flyUpButton:SetAttribute("Pressed", false)
                        end)
                    end

                    -- Connect touch events for down button
                    if flyDownButton and not flyDownButton:GetAttribute("Connected") then
                        flyDownButton:SetAttribute("Connected", true)
                        flyDownButton.MouseButton1Down:Connect(function()
                            flyDownButton:SetAttribute("Pressed", true)
                        end)
                        flyDownButton.MouseButton1Up:Connect(function()
                            flyDownButton:SetAttribute("Pressed", false)
                        end)
                    end

                    -- Check button states
                    if flyUpButton:GetAttribute("Pressed") then
                        velocity = velocity + upDirection * flySpeed
                    end
                    if flyDownButton and flyDownButton:GetAttribute("Pressed") then
                        velocity = velocity - upDirection * flySpeed
                    end
                end
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
        -- Remove mobile controls
        removeMobileControls()
    end

    local flyToggle = Tabs.Main:CreateToggle({
        Name = "Fly",
        CurrentValue = false,
        Flag = "FlyToggle",
        Callback = function(Value)
            flyEnabled = Value
            if flyEnabled then
                enableFly()
            else
                disableFly()
            end
        end,
    })

    -- Anti-AFK Toggle with workspace save
    local antiAfkToggle = Tabs.Main:CreateToggle({
        Name = "Anti-AFK",
        CurrentValue = false,
        Flag = "AntiAfkToggle",
        Callback = function(Value)
            antiAfkEnabled = Value
            saveSettingToWorkspace("AntiAFK", Value)
            if antiAfkEnabled then
                startAntiAfk()
            else
                stopAntiAfk()
            end
        end,
    })

    -- Reset Character Button
    local resetButton = Tabs.Main:CreateButton({
        Name = "Reset Character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end,
    })

    -- Seed stock dropdown (multi-select) with workspace save
    local seedDropdown = Tabs.Seed:CreateDropdown({
        Name = "Select Seeds",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "SeedDropdown",
        Callback = function(Value)
            SelectedSeedStock.Selected = Value
            saveSettingToWorkspace("SelectedSeeds", Value)
            GetSeedStock()
        end
    })

    -- Function to update seed dropdown
    local function updateSeedDropdown()
        local allSeeds = GetSeedStock(false) -- Get all seeds regardless of stock
        local seedList = {}

        for seedName, _ in pairs(allSeeds) do
            table.insert(seedList, seedName)
        end

        seedDropdown:Refresh(seedList, true)
    end

    -- Auto-buy toggle - moved to Seed tab with workspace save
    local autoBuyToggle = Tabs.Seed:CreateToggle({
        Name = "Auto-Buy Seeds",
        CurrentValue = false,
        Flag = "AutoBuyToggle",
        Callback = function(Value)
            autoBuyEnabled = Value
            saveSettingToWorkspace("AutoBuySeeds", Value)
            if autoBuyEnabled then
                startStockChecking()
            else
                stopStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above



    -- Initial seed dropdown update
    updateSeedDropdown()

    -- Load saved seed selections (will be loaded after SaveManager is properly initialized)

    -- Gear stock dropdown (multi-select) with workspace save
    local gearDropdown = Tabs.Gear:CreateDropdown({
        Name = "Select Gears",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "GearDropdown",
        Callback = function(Value)
            SelectedGearStock.Selected = Value
            saveSettingToWorkspace("SelectedGears", Value)
            GetGearStock()
        end,
    })

    -- Function to update gear dropdown
    local function updateGearDropdown()
        local allGears = GetGearStock(false) -- Get all gears regardless of stock
        local gearList = {}

        for gearName, _ in pairs(allGears) do
            table.insert(gearList, gearName)
        end

        gearDropdown:Refresh(gearList, true)
    end

    -- Gear auto-buy toggle with workspace save
    local gearAutoBuyToggle = Tabs.Gear:CreateToggle({
        Name = "Auto-Buy Gears",
        CurrentValue = false,
        Flag = "GearAutoBuyToggle",
        Callback = function(Value)
            gearAutoBuyEnabled = Value
            saveSettingToWorkspace("AutoBuyGears", Value)
            if gearAutoBuyEnabled then
                startGearStockChecking()
            else
                stopGearStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above

    -- Initial gear dropdown update
    updateGearDropdown()

    -- Egg stock dropdown (multi-select) with workspace save
    local eggDropdown = Tabs.Egg:CreateDropdown({
        Name = "Select Eggs",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "EggDropdown",
        Callback = function(Value)
            SelectedEggStock.Selected = Value
            saveSettingToWorkspace("SelectedEggs", Value)
            GetEggStock()
        end,
    })

    -- Function to update egg dropdown
    local function updateEggDropdown()
        local allEggs = GetEggStock(false) -- Get all eggs regardless of stock
        local eggList = {}

        for eggName, _ in pairs(allEggs) do
            table.insert(eggList, eggName)
        end

        eggDropdown:Refresh(eggList, true)
    end

    -- Egg auto-buy toggle with workspace save
    local eggAutoBuyToggle = Tabs.Egg:CreateToggle({
        Name = "Auto-Buy Eggs",
        CurrentValue = false,
        Flag = "EggAutoBuyToggle",
        Callback = function(Value)
            eggAutoBuyEnabled = Value
            saveSettingToWorkspace("AutoBuyEggs", Value)
            if eggAutoBuyEnabled then
                startEggStockChecking()
            else
                stopEggStockChecking()
            end
        end,
    })

    -- Initial egg dropdown update
    updateEggDropdown()

    -- Farm Tab Elements
    -- Farm seed dropdown
    local farmSeedDropdown = Tabs.Farm:CreateDropdown({
        Name = "Select Farm Seed",
        Options = {},
        CurrentOption = "",
        MultipleOptions = false,
        Flag = "FarmSeedDropdown",
        Callback = function(Value)
            SelectedFarmSeed.Selected = Value
            saveSettingToWorkspace("SelectedFarmSeed", Value)
        end,
    })

    -- Function to update farm seed dropdown
    local function updateFarmSeedDropdown()
        GetOwnedSeeds()
        local seedList = {}

        for seedName, _ in pairs(OwnedSeeds) do
            table.insert(seedList, seedName)
        end

        -- Also get all available seeds from shop
        local allSeeds = GetSeedStock(false)
        for seedName, _ in pairs(allSeeds) do
            local found = false
            for _, existingSeed in pairs(seedList) do
                if existingSeed == seedName then
                    found = true
                    break
                end
            end
            if not found then
                table.insert(seedList, seedName)
            end
        end

        farmSeedDropdown:Refresh(seedList, true)
    end

    -- Auto Plant Toggle
    local autoPlantToggle = Tabs.Farm:CreateToggle({
        Name = "Auto Plant",
        CurrentValue = false,
        Flag = "AutoPlantToggle",
        Callback = function(Value)
            AutoPlant = Value
            saveSettingToWorkspace("AutoPlant", Value)
            if AutoPlant then
                startFarmLoops()
            else
                stopFarmLoops()
                startFarmLoops() -- Restart other active loops
            end
        end,
    })

    -- Auto Plant Random Toggle
    local autoPlantRandomToggle = Tabs.Farm:CreateToggle({
        Name = "Plant at Random Points",
        CurrentValue = false,
        Flag = "AutoPlantRandomToggle",
        Callback = function(Value)
            AutoPlantRandom = Value
            saveSettingToWorkspace("AutoPlantRandom", Value)
        end,
    })

    -- Auto Harvest Toggle
    local autoHarvestToggle = Tabs.Farm:CreateToggle({
        Name = "Auto Harvest",
        CurrentValue = false,
        Flag = "AutoHarvestToggle",
        Callback = function(Value)
            AutoHarvest = Value
            saveSettingToWorkspace("AutoHarvest", Value)
            if AutoHarvest then
                startFarmLoops()
            else
                stopFarmLoops()
                startFarmLoops() -- Restart other active loops
            end
        end,
    })

    -- Harvest Ignores Section
    local harvestIgnoreNormal = Tabs.Farm:CreateToggle({
        Name = "Ignore Normal Plants",
        CurrentValue = false,
        Flag = "HarvestIgnoreNormal",
        Callback = function(Value)
            HarvestIgnores.Normal = Value
            saveSettingToWorkspace("HarvestIgnoreNormal", Value)
        end,
    })

    local harvestIgnoreGold = Tabs.Farm:CreateToggle({
        Name = "Ignore Gold Plants",
        CurrentValue = false,
        Flag = "HarvestIgnoreGold",
        Callback = function(Value)
            HarvestIgnores.Gold = Value
            saveSettingToWorkspace("HarvestIgnoreGold", Value)
        end,
    })

    local harvestIgnoreRainbow = Tabs.Farm:CreateToggle({
        Name = "Ignore Rainbow Plants",
        CurrentValue = false,
        Flag = "HarvestIgnoreRainbow",
        Callback = function(Value)
            HarvestIgnores.Rainbow = Value
            saveSettingToWorkspace("HarvestIgnoreRainbow", Value)
        end,
    })

    -- Auto Sell Toggle
    local autoSellToggle = Tabs.Farm:CreateToggle({
        Name = "Auto Sell",
        CurrentValue = false,
        Flag = "AutoSellToggle",
        Callback = function(Value)
            AutoSell = Value
            saveSettingToWorkspace("AutoSell", Value)
        end,
    })

    -- Sell Threshold Slider
    local sellThresholdSlider = Tabs.Farm:CreateSlider({
        Name = "Sell Threshold",
        Range = {1, 199},
        Increment = 1,
        Suffix = " crops",
        CurrentValue = 15,
        Flag = "SellThresholdSlider",
        Callback = function(Value)
            SellThreshold = Value
            saveSettingToWorkspace("SellThreshold", Value)
        end,
    })

    -- Manual Sell Button
    local sellButton = Tabs.Farm:CreateButton({
        Name = "Sell Inventory",
        Callback = function()
            SellInventory()
        end,
    })

    -- Auto Walk Toggle
    local autoWalkToggle = Tabs.Farm:CreateToggle({
        Name = "Auto Walk",
        CurrentValue = false,
        Flag = "AutoWalkToggle",
        Callback = function(Value)
            AutoWalk = Value
            saveSettingToWorkspace("AutoWalk", Value)
            if AutoWalk then
                startFarmLoops()
            else
                stopFarmLoops()
                startFarmLoops() -- Restart other active loops
            end
        end,
    })

    -- Auto Walk Allow Random Toggle
    local autoWalkRandomToggle = Tabs.Farm:CreateToggle({
        Name = "Allow Random Walk Points",
        CurrentValue = true,
        Flag = "AutoWalkRandomToggle",
        Callback = function(Value)
            AutoWalkAllowRandom = Value
            saveSettingToWorkspace("AutoWalkAllowRandom", Value)
        end,
    })

    -- Auto Walk Max Wait Slider
    local autoWalkMaxWaitSlider = Tabs.Farm:CreateSlider({
        Name = "Max Walk Delay",
        Range = {1, 120},
        Increment = 1,
        Suffix = " seconds",
        CurrentValue = 10,
        Flag = "AutoWalkMaxWaitSlider",
        Callback = function(Value)
            AutoWalkMaxWait = Value
            saveSettingToWorkspace("AutoWalkMaxWait", Value)
        end,
    })

    -- NoClip Toggle
    local noClipToggle = Tabs.Farm:CreateToggle({
        Name = "NoClip",
        CurrentValue = false,
        Flag = "NoClipToggle",
        Callback = function(Value)
            NoClip = Value
            saveSettingToWorkspace("NoClip", Value)
            if NoClip then
                startFarmLoops()
            else
                stopFarmLoops()
                startFarmLoops() -- Restart other active loops
            end
        end,
    })

    -- Manual Plant Button
    local plantButton = Tabs.Farm:CreateButton({
        Name = "Plant All Selected Seeds",
        Callback = function()
            AutoPlantLoop()
        end,
    })

    -- Farm Status Label
    local farmStatusLabel = Tabs.Farm:CreateLabel("Status: Ready")

    -- Function to update farm status
    local function updateFarmStatus(message)
        farmStatusLabel:Set("Status: " .. message)
    end

    -- Update the Plant function to show status
    local originalPlant = Plant
    Plant = function(Position, Seed)
        local result = originalPlant(Position, Seed)
        if result then
            updateFarmStatus("Planted " .. Seed .. " at " .. tostring(Position))
        else
            updateFarmStatus("Failed to plant " .. Seed)
        end
        return result
    end

    -- Update AutoPlantLoop to show status
    local originalAutoPlantLoop = AutoPlantLoop
    AutoPlantLoop = function()
        if not AutoPlant then
            updateFarmStatus("Auto plant is disabled")
            return
        end

        local Seed = SelectedFarmSeed.Selected
        if not Seed or Seed == "" then
            updateFarmStatus("No seed selected")
            return
        end

        updateFarmStatus("Starting to plant " .. Seed)
        originalAutoPlantLoop()
    end

    -- Update farm seed dropdown initially
    updateFarmSeedDropdown()

    -- Connect auto-sell check to backpack changes
    if LocalPlayer.Backpack then
        LocalPlayer.Backpack.ChildAdded:Connect(AutoSellCheck)
    end

    -- Function to load saved selections from workspace
    local function loadSavedSelections()
        -- Load saved seed selections
        local savedSeeds = loadSettingFromWorkspace("SelectedSeeds", {})
        if type(savedSeeds) == "table" and #savedSeeds > 0 then
            SelectedSeedStock.Selected = savedSeeds
            seedDropdown:Set(savedSeeds)
        end

        -- Load saved gear selections
        local savedGears = loadSettingFromWorkspace("SelectedGears", {})
        if type(savedGears) == "table" and #savedGears > 0 then
            SelectedGearStock.Selected = savedGears
            gearDropdown:Set(savedGears)
        end

        -- Load saved egg selections
        local savedEggs = loadSettingFromWorkspace("SelectedEggs", {})
        if type(savedEggs) == "table" and #savedEggs > 0 then
            SelectedEggStock.Selected = savedEggs
            eggDropdown:Set(savedEggs)
        end

        -- Load saved toggle states
        local savedAutoBuySeeds = loadSettingFromWorkspace("AutoBuySeeds", false)
        if savedAutoBuySeeds then
            autoBuyToggle:Set(savedAutoBuySeeds)
        end

        local savedAutoBuyGears = loadSettingFromWorkspace("AutoBuyGears", false)
        if savedAutoBuyGears then
            gearAutoBuyToggle:Set(savedAutoBuyGears)
        end

        local savedAutoBuyEggs = loadSettingFromWorkspace("AutoBuyEggs", false)
        if savedAutoBuyEggs then
            eggAutoBuyToggle:Set(savedAutoBuyEggs)
        end

        local savedAntiAFK = loadSettingFromWorkspace("AntiAFK", false)
        if savedAntiAFK then
            antiAfkToggle:Set(savedAntiAFK)
        end

        -- Load saved farm settings
        local savedFarmSeed = loadSettingFromWorkspace("SelectedFarmSeed", "")
        if savedFarmSeed and savedFarmSeed ~= "" then
            SelectedFarmSeed.Selected = savedFarmSeed
            farmSeedDropdown:Set(savedFarmSeed)
        end

        local savedAutoPlant = loadSettingFromWorkspace("AutoPlant", false)
        if savedAutoPlant then
            autoPlantToggle:Set(savedAutoPlant)
        end

        local savedAutoPlantRandom = loadSettingFromWorkspace("AutoPlantRandom", false)
        if savedAutoPlantRandom then
            autoPlantRandomToggle:Set(savedAutoPlantRandom)
        end

        local savedAutoHarvest = loadSettingFromWorkspace("AutoHarvest", false)
        if savedAutoHarvest then
            autoHarvestToggle:Set(savedAutoHarvest)
        end

        local savedAutoSell = loadSettingFromWorkspace("AutoSell", false)
        if savedAutoSell then
            autoSellToggle:Set(savedAutoSell)
        end

        local savedSellThreshold = loadSettingFromWorkspace("SellThreshold", 15)
        if savedSellThreshold then
            sellThresholdSlider:Set(savedSellThreshold)
        end

        local savedAutoWalk = loadSettingFromWorkspace("AutoWalk", false)
        if savedAutoWalk then
            autoWalkToggle:Set(savedAutoWalk)
        end

        local savedAutoWalkRandom = loadSettingFromWorkspace("AutoWalkAllowRandom", true)
        if savedAutoWalkRandom ~= nil then
            autoWalkRandomToggle:Set(savedAutoWalkRandom)
        end

        local savedAutoWalkMaxWait = loadSettingFromWorkspace("AutoWalkMaxWait", 10)
        if savedAutoWalkMaxWait then
            autoWalkMaxWaitSlider:Set(savedAutoWalkMaxWait)
        end

        local savedNoClip = loadSettingFromWorkspace("NoClip", false)
        if savedNoClip then
            noClipToggle:Set(savedNoClip)
        end

        local savedHarvestIgnoreNormal = loadSettingFromWorkspace("HarvestIgnoreNormal", false)
        if savedHarvestIgnoreNormal then
            harvestIgnoreNormal:Set(savedHarvestIgnoreNormal)
        end

        local savedHarvestIgnoreGold = loadSettingFromWorkspace("HarvestIgnoreGold", false)
        if savedHarvestIgnoreGold then
            harvestIgnoreGold:Set(savedHarvestIgnoreGold)
        end

        local savedHarvestIgnoreRainbow = loadSettingFromWorkspace("HarvestIgnoreRainbow", false)
        if savedHarvestIgnoreRainbow then
            harvestIgnoreRainbow:Set(savedHarvestIgnoreRainbow)
        end

        -- Show notification if any settings were loaded
        local hasFarmSettings = savedFarmSeed ~= "" or savedAutoPlant or savedAutoPlantRandom or savedAutoHarvest or savedAutoSell or savedAutoWalk or savedNoClip
        if #savedSeeds > 0 or #savedGears > 0 or #savedEggs > 0 or savedAutoBuySeeds or savedAutoBuyGears or savedAutoBuyEggs or savedAntiAFK or hasFarmSettings then
            Rayfield:Notify({
                Title = "Loaded Settings!",
                Content = "Settings loaded from workspace",
                Duration = 2,
            })
        end
    end

    -- Load saved selections after a delay to ensure GUI is ready
    spawn(function()
        task.wait(2) -- Wait for GUI to fully initialize
        loadSavedSelections()
    end)

-- Settings are now saved in Roblox workspace for persistence across sessions
-- Script loaded successfully with workspace-based settings system